import 'package:flutter/material.dart';

class CreateStoreScreen extends StatefulWidget {
  final Map<String, dynamic>? store;

  CreateStoreScreen({this.store});

  @override
  _CreateStoreScreenState createState() => _CreateStoreScreenState();
}

class _CreateStoreScreenState extends State<CreateStoreScreen> {
  final _formKey = GlobalKey<FormState>();
  late String name;
  late double taxPercent;
  late String rentalHoldMessage;
  late String pricingHeader;
  late String productPriceLabel;
  late String rentalNotAvailable;

  @override
  void initState() {
    super.initState();
    if (widget.store != null) {
      name = widget.store!['name'];
      taxPercent = widget.store!['taxPercent'];
      rentalHoldMessage = widget.store!['rentalHoldMessage'];
      pricingHeader = widget.store!['pricingHeader'];
      productPriceLabel = widget.store!['productPriceLabel'];
      rentalNotAvailable = widget.store!['rentalNotAvailable'];
    } else {
      name = '';
      taxPercent = 0.0;
      rentalHoldMessage = '';
      pricingHeader = '';
      productPriceLabel = '';
      rentalNotAvailable = '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.store == null ? 'Add Store' : 'Edit Store'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                initialValue: name,
                decoration: InputDecoration(
                  labelText: 'Store Name',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                validator: (value) => value!.isEmpty ? 'Enter store name' : null,
                onChanged: (value) => name = value,
              ),
              SizedBox(height: 16),
              TextFormField(
                initialValue: taxPercent.toString(),
                decoration: InputDecoration(
                  labelText: 'Tax Percent',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                keyboardType: TextInputType.number,
                validator: (value) => value!.isEmpty ? 'Enter tax percent' : null,
                onChanged: (value) => taxPercent = double.tryParse(value) ?? 0.0,
              ),
              SizedBox(height: 16),
              TextFormField(
                initialValue: rentalHoldMessage,
                decoration: InputDecoration(
                  labelText: 'Rental Hold Message',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                onChanged: (value) => rentalHoldMessage = value,
              ),
              SizedBox(height: 16),
              TextFormField(
                initialValue: pricingHeader,
                decoration: InputDecoration(
                  labelText: 'Pricing Header',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                onChanged: (value) => pricingHeader = value,
              ),
              SizedBox(height: 16),
              TextFormField(
                initialValue: productPriceLabel,
                decoration: InputDecoration(
                  labelText: 'Product Price Label',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                onChanged: (value) => productPriceLabel = value,
              ),
              SizedBox(height: 16),
              TextFormField(
                initialValue: rentalNotAvailable,
                decoration: InputDecoration(
                  labelText: 'Rental Not Available',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                ),
                onChanged: (value) => rentalNotAvailable = value,
              ),
              SizedBox(height: 20),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                ),
                onPressed: () {
                  if (_formKey.currentState!.validate()) {
                    Navigator.pop(context, {
                      'name': name,
                      'taxPercent': taxPercent,
                      'rentalHoldMessage': rentalHoldMessage,
                      'pricingHeader': pricingHeader,
                      'productPriceLabel': productPriceLabel,
                      'rentalNotAvailable': rentalNotAvailable,
                    });
                  }
                },
                child: Text('Save', style: TextStyle(fontSize: 16)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}