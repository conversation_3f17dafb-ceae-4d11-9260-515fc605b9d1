import 'package:flutter/material.dart';
import 'create_store.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Retail Store Management',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        brightness: Brightness.dark,
        textTheme: TextTheme(
          headlineSmall: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          bodyMedium: TextStyle(fontSize: 16),
        ),
      ),
      home: StoreListScreen(),
    );
  }
}

class StoreListScreen extends StatefulWidget {
  @override
  _StoreListScreenState createState() => _StoreListScreenState();
}

class _StoreListScreenState extends State<StoreListScreen> {
  final List<Map<String, dynamic>> _stores = [
    {
      'id': '1',
      'name': 'Store 1',
      'taxPercent': 5.0,
      'rentalHoldMessage': 'Hold confirmed',
      'pricingHeader': 'Pricing Details',
      'productPriceLabel': 'Price: \$100',
      'rentalNotAvailable': 'Not available',
      'companyAddress': '123 Main Street, City, State 12345',
      'contactDetails': 'Phone: (*************, Email: <EMAIL>',
    },
  ];
  late List<Map<String, dynamic>> _filteredStores;

  @override
  void initState() {
    super.initState();
    _filteredStores = List.from(_stores);
  }

  void _addStore(Map<String, dynamic> newStore) {
    setState(() {
      // Generate a unique ID for the new store
      newStore['id'] = DateTime.now().millisecondsSinceEpoch.toString();
      _stores.add(newStore);
      _filteredStores = List.from(_stores);
    });
  }

  void _updateStore(int filteredIndex, Map<String, dynamic> updatedStore) {
    setState(() {
      // Find the actual index in _stores by matching the store ID
      final storeToUpdate = _filteredStores[filteredIndex];
      final actualIndex = _stores.indexWhere(
        (store) => store['id'] == storeToUpdate['id'],
      );

      if (actualIndex != -1) {
        // Preserve the original ID
        updatedStore['id'] = storeToUpdate['id'];
        _stores[actualIndex] = updatedStore;
        _filteredStores = List.from(_stores);
      }
    });
  }

  void _deleteStore(int filteredIndex) {
    setState(() {
      // Find the actual index in _stores by matching the store ID
      final storeToDelete = _filteredStores[filteredIndex];
      final actualIndex = _stores.indexWhere(
        (store) => store['id'] == storeToDelete['id'],
      );

      if (actualIndex != -1) {
        _stores.removeAt(actualIndex);
        _filteredStores = List.from(_stores);
      }
    });
  }

  void _filterStores(String query) {
    setState(() {
      _filteredStores = _stores
          .where(
            (store) =>
                store['name'].toLowerCase().contains(query.toLowerCase()),
          )
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Retail Stores'),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: () =>
                setState(() => _filteredStores = List.from(_stores)),
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              decoration: InputDecoration(
                labelText: 'Search Stores',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onChanged: _filterStores,
            ),
          ),
          Expanded(
            child: _filteredStores.isEmpty
                ? Center(
                    child: Text(
                      'No stores available',
                      style: TextStyle(fontSize: 18),
                    ),
                  )
                : ListView.builder(
                    padding: EdgeInsets.all(8.0),
                    itemCount: _filteredStores.length,
                    itemBuilder: (context, index) {
                      return Card(
                        elevation: 4,
                        margin: EdgeInsets.symmetric(vertical: 8),
                        child: ListTile(
                          contentPadding: EdgeInsets.all(16),
                          title: Text(
                            _filteredStores[index]['name'],
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Tax: ${_filteredStores[index]['taxPercent']}%',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              Text(
                                _filteredStores[index]['productPriceLabel'],
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              if (_filteredStores[index]['companyAddress'] !=
                                      null &&
                                  _filteredStores[index]['companyAddress']
                                      .isNotEmpty)
                                Text(
                                  'Address: ${_filteredStores[index]['companyAddress']}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              if (_filteredStores[index]['contactDetails'] !=
                                      null &&
                                  _filteredStores[index]['contactDetails']
                                      .isNotEmpty)
                                Text(
                                  'Contact: ${_filteredStores[index]['contactDetails']}',
                                  style: Theme.of(context).textTheme.bodyMedium,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                            ],
                          ),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                icon: Icon(Icons.edit, color: Colors.blue),
                                onPressed: () async {
                                  final updatedStore = await Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => CreateStoreScreen(
                                        store: _filteredStores[index],
                                      ),
                                    ),
                                  );
                                  if (updatedStore != null) {
                                    _updateStore(index, updatedStore);
                                  }
                                },
                              ),
                              IconButton(
                                icon: Icon(Icons.delete, color: Colors.red),
                                onPressed: () => _deleteStore(index),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final newStore = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => CreateStoreScreen()),
          );
          if (newStore != null) _addStore(newStore);
        },
        backgroundColor: Colors.green,
        child: Icon(Icons.add),
      ),
    );
  }
}
